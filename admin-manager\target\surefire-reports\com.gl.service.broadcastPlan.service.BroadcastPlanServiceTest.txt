-------------------------------------------------------------------------------
Test set: com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest
-------------------------------------------------------------------------------
Tests run: 18, Failures: 0, Errors: 8, Skipped: 0, Time elapsed: 2.206 s <<< FAILURE! - in com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest
testAddOrUpdate_EmptyVoiceWorkList_ShouldReturnFailure  Time elapsed: 0.326 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testAddOrUpdate_EmptyVoiceWorkList_ShouldReturnFailure(BroadcastPlanServiceTest.java:288)

testList_WhenNoResults_ShouldReturnEmptyData  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`name`)) deviceNames,
 GROUP_CONCAT(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  and s.id in (?, ?)
 GROUP BY b.id ) t",
    class java.lang.Long,
    1L,
    2L
);
    -> at com.gl.service.broadcastPlan.service.BroadcastPlanService.list(BroadcastPlanService.java:92)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_WhenNoResults_ShouldReturnEmptyData(BroadcastPlanServiceTest.java:128)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_WhenNoResults_ShouldReturnEmptyData(BroadcastPlanServiceTest.java:132)

testAddOrUpdate_NullDto_ShouldReturnFailure  Time elapsed: 0.099 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testAddOrUpdate_NullDto_ShouldReturnFailure(BroadcastPlanServiceTest.java:224)

testList_WithSearchCondition_ShouldReturnFilteredResults  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`name`)) deviceNames,
 GROUP_CONCAT(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  and ( d.sn like ? )  and s.id in (?, ?)
 GROUP BY b.id ) t",
    class java.lang.Long,
    "%test%",
    1L,
    2L
);
    -> at com.gl.service.broadcastPlan.service.BroadcastPlanService.list(BroadcastPlanService.java:92)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_WithSearchCondition_ShouldReturnFilteredResults(BroadcastPlanServiceTest.java:103)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_WithSearchCondition_ShouldReturnFilteredResults(BroadcastPlanServiceTest.java:111)

testAddOrUpdate_EmptyStartTime_ShouldReturnFailure  Time elapsed: 0.095 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testAddOrUpdate_EmptyStartTime_ShouldReturnFailure(BroadcastPlanServiceTest.java:240)

testAddOrUpdate_EmptyEndTime_ShouldReturnFailure  Time elapsed: 0.092 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testAddOrUpdate_EmptyEndTime_ShouldReturnFailure(BroadcastPlanServiceTest.java:256)

testAddOrUpdate_NullDeviceIds_ShouldReturnFailure  Time elapsed: 0.105 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testAddOrUpdate_NullDeviceIds_ShouldReturnFailure(BroadcastPlanServiceTest.java:272)

testList_ExportMode_ShouldNotPaginate  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (SELECT
   b.id,
   b.start_time,
   b.end_time,
   s.shop_name,
 b.create_time,s.id shopId,
 GROUP_CONCAT(DISTINCT( d.`id`)) deviceIds,
 GROUP_CONCAT(DISTINCT(d.`name`)) deviceNames,
 GROUP_CONCAT(DISTINCT(v.`title`))  titles , 
 b.type,b.start_date,b.end_date,b.interval_time  FROM 
  dub_broadcast_plan b  -- 计划
  left join dub_shop s on s.id=b.shop_id -- 门店
  left join dub_device d on  FIND_IN_SET(d.id,b.device_ids)  -- 设备
  left join dub_broadcast_plan_voice_work_ref br on br.plan_id = b.id   -- 关联作品
  left join dub_voice_work v on v.id = br.voice_work_id  -- 作品
where 1=1  and s.id in (?)
 GROUP BY b.id ) t",
    class java.lang.Long,
    1L
);
    -> at com.gl.service.broadcastPlan.service.BroadcastPlanService.list(BroadcastPlanService.java:92)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_ExportMode_ShouldNotPaginate(BroadcastPlanServiceTest.java:149)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.broadcastPlan.service.BroadcastPlanServiceTest.testList_ExportMode_ShouldNotPaginate(BroadcastPlanServiceTest.java:157)

